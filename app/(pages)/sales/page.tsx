"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import {
    <PERSON>ton,
    FormCard,
    FormGrid,
    Input,
    Pagination,
    SearchInput,
    Select,
} from "../../../lib/forms";
import { useServerPagination } from "../../../lib/pagination";

// Count-up animation hook
function useCountUp(end: number, duration: number = 2000) {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration]);

  return count;
}

interface Sale {
  id: number;
  total: number;
  subtotal: number;
  tax: number | null;
  discount: number | null;
  paymentType: string;
  status: string;
  notes: string | null;
  createdAt: string;
  customer: {
    id: number;
    name: string;
    email: string | null;
    phone: string | null;
  } | null;
  user: {
    id: number;
    name: string;
    email: string;
  };
  saleItems: Array<{
    id: number;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    product: {
      id: number;
      name: string;
      sku: string | null;
      price: number;
    };
  }>;
}

interface SalesResponse {
  sales: Sale[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalCount: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  summary: {
    totalSales: number;
    totalTax: number;
    totalDiscount: number;
    averageOrderValue: number;
    totalTransactions: number;
  };
}

export default function SalesPage() {
  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [summary, setSummary] = useState({
    totalSales: 0,
    totalTax: 0,
    totalDiscount: 0,
    averageOrderValue: 0,
    totalTransactions: 0,
  });
  const [filters, setFilters] = useState({
    search: "",
    dateFrom: "",
    dateTo: "",
    paymentMethod: "",
    status: "",
  });

  // Initialize pagination
  const pagination = useServerPagination({
    totalItems,
    initialPageSize: 10,
    useUrlParams: true,
  });

  // Count-up animations for stats
  const totalSales = useCountUp(summary.totalSales * 100) / 100;
  const totalTransactions = useCountUp(summary.totalTransactions);
  const averageSale = useCountUp(summary.averageOrderValue * 100) / 100;
  const cashSales = useCountUp(
    sales
      .filter((sale) => sale.paymentType.toLowerCase() === "cash")
      .reduce((sum, sale) => sum + sale.total, 0) * 100,
  ) / 100;

  const paymentMethodOptions = [
    { value: "", label: "All Methods" },
    { value: "cash", label: "Cash" },
    { value: "credit", label: "Credit Card" },
    { value: "debit", label: "Debit Card" },
    { value: "mobile", label: "Mobile Payment" },
  ];

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "completed", label: "Completed" },
    { value: "pending", label: "Pending" },
    { value: "refunded", label: "Refunded" },
    { value: "cancelled", label: "Cancelled" },
  ];

  // Fetch sales from API
  useEffect(() => {
    const fetchSales = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const params = new URLSearchParams({
          page: pagination.currentPage.toString(),
          limit: pagination.pageSize.toString(),
        });

        if (filters.search) params.set("search", filters.search);
        if (filters.dateFrom) params.set("startDate", filters.dateFrom);
        if (filters.dateTo) params.set("endDate", filters.dateTo);
        if (filters.paymentMethod) params.set("paymentMethod", filters.paymentMethod);
        if (filters.status) params.set("status", filters.status);

        const response = await fetch(`/api/sales?${params.toString()}`);
        if (!response.ok) {
          throw new Error("Failed to fetch sales");
        }

        const data: SalesResponse = await response.json();
        setSales(data.sales);
        setTotalItems(data.pagination.totalCount);
        setSummary(data.summary);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchSales();
  }, [pagination.currentPage, pagination.pageSize, filters]);

  const handleSearch = (query: string) => {
    setFilters((prev) => ({ ...prev, search: query }));
    // Reset to first page when searching
    pagination.setPage(1);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    // Reset to first page when filtering
    pagination.setPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Sales & Transactions
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              View and manage all sales transactions
            </p>
          </div>
          <Link href="/sales/new">
            <Button
              variant="success"
              icon={
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              }
            >
              New Sale
            </Button>
          </Link>
        </div>
      </div>

      {/* Sales Summary Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Today's Sales
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${totalSales.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Transactions
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    {totalTransactions}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-yellow-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Avg. Sale
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${averageSale.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg
                  className="h-6 w-6 text-purple-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500 dark:text-gray-400">
                    Cash Sales
                  </dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">
                    ${cashSales.toFixed(2)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <FormCard title="Search & Filter Sales">
        <FormGrid cols={5} gap="md">
          <SearchInput
            name="search"
            label="Search Sales"
            placeholder="Search by transaction ID or customer..."
            value={filters.search}
            onSearch={handleSearch}
            debounceMs={300}
            showClearButton
          />

          <Input
            name="dateFrom"
            label="From Date"
            type="date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
          />

          <Input
            name="dateTo"
            label="To Date"
            type="date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange("dateTo", e.target.value)}
          />

          <Select
            name="paymentMethod"
            label="Payment Method"
            options={paymentMethodOptions}
            value={filters.paymentMethod}
            onChange={(e) => handleFilterChange("paymentMethod", e.target.value)}
          />

          <div className="flex items-end">
            <Button variant="primary" className="w-full">
              Filter
            </Button>
          </div>
        </FormGrid>
      </FormCard>

      {/* Sales Table */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Transaction ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Items
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Total
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Payment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {loading ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    Loading sales...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-6 py-4 text-center text-red-500"
                  >
                    Error: {error}
                  </td>
                </tr>
              ) : sales.length === 0 ? (
                <tr>
                  <td
                    colSpan={7}
                    className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                  >
                    No sales found
                  </td>
                </tr>
              ) : (
                sales.map((sale) => {
                  const saleDate = new Date(sale.createdAt);
                  const customerName = sale.customer?.name || "Walk-in Customer";
                  const itemCount = sale.saleItems.reduce((sum, item) => sum + item.quantity, 0);

                  return (
                    <tr
                      key={sale.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        #{sale.id}
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                        <div>
                          <div>{saleDate.toLocaleDateString()}</div>
                          <div className="text-gray-500 dark:text-gray-400">
                            {saleDate.toLocaleTimeString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                        {customerName}
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                        {itemCount}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900 dark:text-white">
                        ₵{sale.total.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                        {sale.paymentType}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                        <button className="mr-4 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                          View
                        </button>
                        <button className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                          Receipt
                        </button>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {!loading && !error && totalItems > 0 && (
        <Pagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.setPage}
          showInfo={true}
          showPageSize={true}
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.setPageSize}
          totalItems={totalItems}
        />
      )}
    </div>
  );
}
