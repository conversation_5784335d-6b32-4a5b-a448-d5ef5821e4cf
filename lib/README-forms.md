# 📋 Forms Library

A comprehensive, single-file forms library for React applications with TypeScript, Tailwind CSS, and accessibility built-in.

## 🎯 Why Single File?

- **Easy to import**: One import statement for all form components
- **No dependency hell**: Everything in one place
- **Easy to customize**: Modify the entire library in one file
- **Better tree-shaking**: Import only what you need
- **Consistent API**: All components follow the same patterns

## 📦 What's Included

### Core Components

- `Input` - Text, email, password, number variants
- `Select` - Dropdown with options
- `Textarea` - Multi-line text input
- `Checkbox` - Single checkbox with label
- `Button` - Multiple variants and states

### Specialized Components

- `CurrencyInput` - Formatted currency with locale support
- `SearchInput` - Debounced search with clear functionality
- `CategorySelect` - Pre-built product categories
- `CurrencySelect` - Currency selection dropdown

### Layout Components

- `FormCard` - Card wrapper for forms
- `FormGrid` - Responsive grid layout
- `FormSection` - Section with title and description
- `FormActions` - Button group for form actions
- `FormDivider` - Visual separator

### Pagination Components

- `Pagination` - Complete pagination with navigation controls
- `PaginationInfo` - Display pagination information
- `PaginationSelect` - Page size selector

## 🚀 Quick Start

```tsx
import {
  FormCard,
  FormGrid,
  Input,
  EmailInput,
  SubmitButton,
} from "@/lib/forms";

function MyForm() {
  return (
    <FormCard title="Contact Form">
      <form className="space-y-4">
        <FormGrid cols={2}>
          <Input name="name" label="Name" required />
          <EmailInput name="email" label="Email" required />
        </FormGrid>
        <SubmitButton>Submit</SubmitButton>
      </form>
    </FormCard>
  );
}
```

## ✨ Key Features

### 🎨 **Beautiful Design**

- Consistent styling with Tailwind CSS
- Dark mode support out of the box
- Responsive design for all screen sizes
- Modern, clean aesthetics

### ♿ **Accessibility First**

- ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Error announcements

### 🔧 **Developer Experience**

- Full TypeScript support
- IntelliSense for all props
- Consistent API across components
- Easy to customize and extend

### 📱 **Mobile Ready**

- Touch-friendly sizing
- Responsive layouts
- Mobile-optimized interactions
- Proper viewport handling

### 🔍 **Validation Ready**

- Built-in error display
- Help text support
- Visual feedback for states
- Works with any validation library

## 📚 Documentation

- [`forms-documentation.md`](./forms-documentation.md) - Complete API reference
- [`forms-examples.tsx`](./forms-examples.tsx) - Working examples
- [`forms.tsx`](./forms.tsx) - Source code with inline comments

## 🎯 Usage Examples

### Product Form

```tsx
import {
  FormCard,
  FormGrid,
  Input,
  CategorySelect,
  CurrencyInput,
} from "@/lib/forms";

<FormCard title="Add Product">
  <FormGrid cols={2}>
    <Input name="name" label="Product Name" required />
    <CategorySelect name="category" label="Category" />
    <CurrencyInput name="price" label="Price" required />
    <Input name="sku" label="SKU" />
  </FormGrid>
</FormCard>;
```

### Search Interface

```tsx
import { SearchInput, Select, Button } from "@/lib/forms";

<SearchInput
  name="search"
  placeholder="Search products..."
  onSearch={handleSearch}
  debounceMs={300}
/>;
```

### User Registration

```tsx
import {
  FormCard,
  Input,
  EmailInput,
  PasswordInput,
  Checkbox,
  SubmitButton,
} from "@/lib/forms";

<FormCard title="Sign Up">
  <Input name="name" label="Full Name" required />
  <EmailInput name="email" label="Email" required />
  <PasswordInput name="password" label="Password" required />
  <Checkbox name="terms" label="I agree to the terms" required />
  <SubmitButton>Create Account</SubmitButton>
</FormCard>;
```

## 🔧 Customization

### Custom Styling

```tsx
<Input name="custom" className="border-purple-300 focus:border-purple-500" />
```

### Button Variants

```tsx
<Button variant="primary">Primary</Button>
<Button variant="danger">Delete</Button>
<Button variant="ghost">Cancel</Button>
```

### Responsive Grids

```tsx
<FormGrid cols={1} className="sm:grid-cols-2 lg:grid-cols-3">
  {/* Responsive layout */}
</FormGrid>
```

### Pagination

```tsx
import { Pagination, PaginationInfo, PaginationSelect } from "@/lib/forms";

<Pagination
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
  showInfo={true}
  showPageSize={true}
  pageSize={pageSize}
  onPageSizeChange={setPageSize}
  totalItems={totalItems}
/>;
```

## 🔗 Integration

Works seamlessly with:

- **React Hook Form** - Form state management
- **Zod** - Schema validation
- **Next.js** - Server-side rendering
- **Tailwind CSS** - Styling system
- **TypeScript** - Type safety

## 📈 Performance

- **Tree-shakable** - Import only what you need
- **Lightweight** - No external dependencies
- **Fast rendering** - Optimized React components
- **Minimal bundle size** - Single file approach

## 🛠 Maintenance

- **Single source of truth** - All components in one file
- **Easy updates** - Modify one file to update everything
- **Version control friendly** - Clear change tracking
- **No breaking changes** - Consistent API design

## 🎉 Benefits

1. **Productivity** - Build forms faster with pre-built components
2. **Consistency** - Uniform design across your application
3. **Accessibility** - Built-in a11y features
4. **Maintainability** - Single file to manage
5. **Flexibility** - Easy to customize and extend
6. **Type Safety** - Full TypeScript support
7. **Performance** - Optimized for React and modern bundlers

## 🚀 Getting Started

1. Copy `lib/forms.tsx` to your project
2. Import components as needed
3. Start building beautiful forms!

```tsx
import { FormCard, Input, SubmitButton } from "@/lib/forms";

// You're ready to go! 🎉
```

---

**Built with ❤️ for modern React applications**
