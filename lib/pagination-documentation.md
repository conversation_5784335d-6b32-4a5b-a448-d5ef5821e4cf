# Pagination Utilities Documentation

This document describes the pagination utilities and hooks available in the application.

## Overview

The pagination system provides a comprehensive set of utilities and hooks for implementing both client-side and server-side pagination with URL synchronization, search functionality, and sorting capabilities.

## Components

### Pagination Components (from forms library)

- **Pagination**: Complete pagination component with navigation controls
- **PaginationInfo**: Display pagination information (showing X to Y of Z results)
- **PaginationSelect**: Page size selector dropdown

## Hooks

### `usePagination`

Core pagination hook with optional URL synchronization.

```tsx
import { usePagination } from "@/lib/pagination";

const pagination = usePagination({
  totalItems: 100,
  initialPage: 1,
  initialPageSize: 10,
  useUrlParams: true, // Sync with URL parameters
  pageParam: "page", // URL parameter name for page
  pageSizeParam: "pageSize", // URL parameter name for page size
});
```

**Returns:**
- `currentPage`: Current page number
- `pageSize`: Items per page
- `totalItems`: Total number of items
- `totalPages`: Total number of pages
- `canGoNext`: Whether next page is available
- `canGoPrevious`: Whether previous page is available
- `startItem`: First item number on current page
- `endItem`: Last item number on current page
- `setPage(page)`: Navigate to specific page
- `setPageSize(size)`: Change page size
- `nextPage()`: Go to next page
- `previousPage()`: Go to previous page
- `goToFirstPage()`: Go to first page
- `goToLastPage()`: Go to last page

### `useServerPagination`

Hook for server-side pagination with API integration.

```tsx
import { useServerPagination } from "@/lib/pagination";

const pagination = useServerPagination({
  totalItems: data.pagination.totalCount,
  initialPageSize: 10,
  useUrlParams: true,
});

// Use in API calls
useEffect(() => {
  const fetchData = async () => {
    const params = new URLSearchParams({
      page: pagination.currentPage.toString(),
      limit: pagination.pageSize.toString(),
    });
    
    const response = await fetch(`/api/data?${params.toString()}`);
    // Handle response...
  };
  
  fetchData();
}, [pagination.currentPage, pagination.pageSize]);
```

**Additional properties:**
- `offset`: Calculated offset for database queries
- `limit`: Page size (alias for pageSize)
- `getQueryParams()`: Helper function returning `{ page, limit, offset }`

### `useClientPagination`

Hook for client-side pagination of array data.

```tsx
import { useClientPagination } from "@/lib/pagination";

const { data: paginatedData, ...pagination } = useClientPagination(
  allData,
  10, // page size
  true // use URL params
);
```

**Returns:** All pagination properties plus:
- `data`: Paginated slice of the original data array

### `usePaginationWithSearch`

Hook combining pagination with search functionality.

```tsx
import { usePaginationWithSearch } from "@/lib/pagination";

const {
  data: paginatedData,
  searchQuery,
  setSearchQuery,
  totalUnfilteredItems,
  ...pagination
} = usePaginationWithSearch(
  allData,
  ["name", "email", "description"], // searchable fields
  10 // page size
);
```

**Additional properties:**
- `searchQuery`: Current search query
- `setSearchQuery(query)`: Update search query
- `totalUnfilteredItems`: Total items before search filtering

### `useSortablePagination`

Hook combining pagination with sorting functionality.

```tsx
import { useSortablePagination } from "@/lib/pagination";

const {
  data: paginatedData,
  sortConfig,
  handleSort,
  ...pagination
} = useSortablePagination(allData, 10);

// In table header
<th onClick={() => handleSort("name")}>
  Name {sortConfig.key === "name" && (sortConfig.direction === "asc" ? "↑" : "↓")}
</th>
```

**Additional properties:**
- `sortConfig`: Current sort configuration `{ key, direction }`
- `handleSort(key)`: Sort by specified field

## Utility Functions

### `calculatePagination`

Calculate pagination metadata.

```tsx
import { calculatePagination } from "@/lib/pagination";

const pagination = calculatePagination(currentPage, pageSize, totalItems);
```

### `getPaginationSlice`

Get paginated slice of array data.

```tsx
import { getPaginationSlice } from "@/lib/pagination";

const paginatedData = getPaginationSlice(data, currentPage, pageSize);
```

### `buildPaginationQuery`

Build URL query parameters for pagination.

```tsx
import { buildPaginationQuery } from "@/lib/pagination";

const queryParams = buildPaginationQuery(currentPage, pageSize);
```

## Usage Examples

### Basic Server-Side Pagination

```tsx
import { Pagination } from "@/lib/forms";
import { useServerPagination } from "@/lib/pagination";

function ProductsPage() {
  const [products, setProducts] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  
  const pagination = useServerPagination({
    totalItems,
    initialPageSize: 10,
    useUrlParams: true,
  });

  useEffect(() => {
    // Fetch data when pagination changes
    fetchProducts();
  }, [pagination.currentPage, pagination.pageSize]);

  return (
    <div>
      {/* Display products */}
      {products.map(product => (
        <div key={product.id}>{product.name}</div>
      ))}
      
      {/* Pagination controls */}
      <Pagination
        currentPage={pagination.currentPage}
        totalPages={pagination.totalPages}
        onPageChange={pagination.setPage}
        showInfo={true}
        showPageSize={true}
        pageSize={pagination.pageSize}
        onPageSizeChange={pagination.setPageSize}
        totalItems={totalItems}
      />
    </div>
  );
}
```

### Client-Side Pagination with Search

```tsx
import { SearchInput, Pagination } from "@/lib/forms";
import { usePaginationWithSearch } from "@/lib/pagination";

function UsersPage() {
  const [allUsers] = useState(/* fetch all users */);
  
  const {
    data: paginatedUsers,
    searchQuery,
    setSearchQuery,
    ...pagination
  } = usePaginationWithSearch(
    allUsers,
    ["name", "email"],
    10
  );

  return (
    <div>
      <SearchInput
        value={searchQuery}
        onSearch={setSearchQuery}
        placeholder="Search users..."
      />
      
      {paginatedUsers.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
      
      <Pagination {...pagination} />
    </div>
  );
}
```

## API Response Format

For server-side pagination, APIs should return data in this format:

```json
{
  "data": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalCount": 100,
    "limit": 10,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Best Practices

1. **Use URL parameters** for pagination state to enable bookmarking and sharing
2. **Reset to page 1** when applying filters or search
3. **Show loading states** during data fetching
4. **Handle empty states** gracefully
5. **Use appropriate page sizes** for different layouts (tables vs grids)
6. **Provide page size options** for user flexibility
7. **Include pagination info** to show current position and total items
